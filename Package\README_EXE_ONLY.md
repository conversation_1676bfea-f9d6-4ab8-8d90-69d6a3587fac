# 📦 BUILD EXÉCUTABLE SEULEMENT - SUIVI PLAN ADRESSAGE

## 🎯 Vue d'ensemble
Ce guide vous explique comment créer **seulement l'exécutable** (.exe) de l'application Suivi Plan Adressage avec PyInstaller, **sans installateur**.

## ✅ Pourquoi xlrd est nécessaire ?
Votre application utilise `pd.read_excel()` qui peut lire :
- **Fichiers .xlsx** → utilise `openpyxl` 
- **Fichiers .xls** (anciens formats Excel) → utilise `xlrd`

C'est pourquoi `xlrd>=2.0.0` a été ajouté aux dépendances.

## 🚀 Méthodes de Build

### **Méthode 1 : Script Automatique (RECOMMANDÉ)**
```bash
# Double-cliquer sur :
build_exe_only.bat
```

### **Méthode 2 : Script Python**
```bash
cd Package
python build_exe_only.py
```

### **Méthode 3 : Commande PyInstaller directe**
```bash
cd Package
pip install -r requirements.txt
pyinstaller --onefile --windowed --clean --noconfirm ^
    --name=Suivi_Plan_Adressage ^
    --icon=../logo-2024.png ^
    --add-data="../logo-2024.png;." ^
    --hidden-import=pandas ^
    --hidden-import=openpyxl ^
    --hidden-import=xlrd ^
    --hidden-import=PIL ^
    --hidden-import=PIL.Image ^
    --hidden-import=PIL.ImageTk ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.ttk ^
    ../Code_App.py
```

## 📁 Résultat

Après exécution, vous obtiendrez :
```
Package/
└── dist/
    └── Suivi_Plan_Adressage.exe    # Exécutable standalone (~50-80 MB)
```

## ✨ Avantages de cette approche

- ✅ **Un seul fichier** : `Suivi_Plan_Adressage.exe`
- ✅ **Portable** : Fonctionne sur n'importe quel PC Windows
- ✅ **Aucune installation** requise
- ✅ **Toutes les dépendances** incluses
- ✅ **Logo intégré** dans l'exécutable

## 🚀 Distribution

Pour distribuer votre application :

1. **Copiez** `dist/Suivi_Plan_Adressage.exe`
2. **Envoyez** par email, clé USB, ou serveur
3. **L'utilisateur** double-clique pour lancer
4. **Aucune installation** nécessaire !

## 🔧 Dépendances incluses

L'exécutable contient automatiquement :
- Python runtime
- pandas + xlrd + openpyxl (pour Excel)
- Pillow (pour les images)
- tkinter (interface graphique)
- Votre logo (logo-2024.png)

## 📊 Comparaison des approches

| Aspect | Exécutable seul | Avec Installateur |
|--------|----------------|-------------------|
| **Simplicité** | ✅ Très simple | ⚠️ Plus complexe |
| **Taille** | 📁 50-80 MB | 📁 50-80 MB + setup.exe |
| **Distribution** | ✅ Un seul fichier | ⚠️ Deux fichiers |
| **Installation** | ✅ Aucune | ⚠️ Requise |
| **Désinstallation** | ✅ Supprimer le fichier | ⚠️ Via Programmes |
| **Raccourcis** | ❌ Manuels | ✅ Automatiques |
| **Registre Windows** | ✅ Aucune modification | ⚠️ Entrées créées |

## 🐛 Résolution de problèmes

### Erreur "Module not found"
Ajoutez le module manquant dans `build_exe_only.py` :
```python
"--hidden-import=votre_module_manquant",
```

### Exécutable trop volumineux
- Normal pour une app Python complète (50-80 MB)
- Contient tout Python + dépendances
- Optionnel : Utilisez UPX pour compresser

### Erreur au lancement
- Vérifiez que `logo-2024.png` existe
- Testez d'abord le script Python original
- Consultez les logs dans la console

## 📋 Checklist

- [ ] Python 3.8+ installé
- [ ] Fichier `logo-2024.png` présent
- [ ] Script `Code_App.py` fonctionne
- [ ] Dépendances installées (`pip install -r requirements.txt`)
- [ ] PyInstaller installé
- [ ] Build réussi
- [ ] Exécutable testé

## 💡 Conseils

1. **Testez** l'exécutable sur un autre PC pour vérifier
2. **Gardez** une copie du dossier Package pour futurs builds
3. **Documentez** la version pour vos utilisateurs
4. **Créez** un raccourci avec une icône si nécessaire

## 🆚 Si vous voulez quand même un installateur plus tard

Vous pouvez toujours utiliser les autres scripts :
- `build_app.py` + Inno Setup
- `package_complete.bat`

Mais pour la simplicité, l'exécutable seul est souvent suffisant !
