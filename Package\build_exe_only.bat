@echo off
chcp 65001 >nul
echo ========================================
echo    BUILD EXECUTABLE SEULEMENT
echo    SUIVI PLAN ADRESSAGE v2.0
echo ========================================
echo.

REM Verifier si Python est installe
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python depuis https://python.org
    pause
    exit /b 1
)

echo [OK] Python detecte
echo.

REM Aller dans le dossier Package
cd /d "%~dp0"

REM Executer le script de build
echo [1/1] Creation de l'executable...
python build_exe_only.py
if errorlevel 1 (
    echo.
    echo ERREUR: Echec de la creation de l'executable
    echo Verifiez les messages d'erreur ci-dessus
    pause
    exit /b 1
)

REM Verifier si l'executable a ete cree
if not exist "dist\Suivi_Plan_Adressage.exe" (
    echo.
    echo ERREUR: Executable non trouve dans dist\
    pause
    exit /b 1
)

echo.
echo ========================================
echo         BUILD TERMINE!
echo ========================================
echo.
echo Executable cree: dist\Suivi_Plan_Adressage.exe
echo.
echo UTILISATION:
echo    - Copiez le fichier .exe sur n'importe quel PC Windows
echo    - Double-cliquez pour lancer l'application
echo    - Aucune installation requise!
echo.
echo Emplacement: %~dp0dist\Suivi_Plan_Adressage.exe
echo.
pause
