# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['..\\Code_App.py'],
    pathex=[],
    binaries=[],
    datas=[('../Icone_App.png', '.'), ('../logo-2024.png', '.')],
    hiddenimports=['pandas', 'openpyxl', 'xlrd', 'PIL', 'PIL.Image', 'PIL.ImageTk', 'tkinter', 'tkinter.filedialog', 'tkinter.messagebox', 'tkinter.ttk'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Suivi_Generator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['..\\Icone_App.png'],
)
